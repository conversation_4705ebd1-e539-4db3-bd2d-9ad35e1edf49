{"name": "red-planet", "version": "0.1.0", "description": "Red Planet", "author": "Clipboard", "private": true, "license": "UNLICENSED", "scripts": {"setup:server": "cd server && npm install", "setup:client": "cd client && npm install", "setup": "npm run setup:server && npm run setup:client", "server:build": "cd server && npm run build", "server:start": "cd server && npm run start", "server:start:dev": "cd server && npm run start:dev", "server:start:debug": "cd server && npm run start:debug", "server:start:prod": "cd server && npm run start:prod", "start:topWorkplaces": "cd server && npm run start:topWorkplaces", "start:topWorkplaces:ts": "cd server && npm run start:topWorkplaces:ts", "start:topWorkplaces:js": "cd server && npm run start:topWorkplaces:js", "client:start:dev": "cd client && npm run start:dev", "client:build": "cd client && npm run build", "client:lint": "cd client && npm run lint", "client:preview": "cd client && npm run preview"}, "devDependencies": {"ts-node": "10.9.2", "typescript": "^5.1.3"}}