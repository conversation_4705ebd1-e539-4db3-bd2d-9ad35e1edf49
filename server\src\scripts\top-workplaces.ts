import axios from 'axios';

interface Workplace {
  id: number;
  name: string;
  status: number;
}

interface Shift {
  id: number;
  workplaceId: number;
  workerId: number | null;
  startAt: string;
  endAt: string;
  cancelledAt: string | null;
}

interface PaginatedResponse<T> {
  data: T[];
  links: { next?: string };
}

interface TopWorkplace {
  name: string;
  shifts: number;
}

const API_BASE_URL = 'http://localhost:3001';

async function fetchAllWorkplaces(): Promise<Workplace[]> {
  const workplaces: Workplace[] = [];
  let nextUrl: string | null = `${API_BASE_URL}/workplaces`;

  while (nextUrl) {
    const response: { data: PaginatedResponse<Workplace> } = await axios.get<PaginatedResponse<Workplace>>(nextUrl);
    workplaces.push(...response.data.data);
    nextUrl = response.data.links.next ? `${API_BASE_URL}${response.data.links.next}` : null;
  }

  return workplaces;
}

async function fetchAllShifts(): Promise<Shift[]> {
  const shifts: Shift[] = [];
  let nextUrl: string | null = `${API_BASE_URL}/shifts`;

  while (nextUrl) {
    const response: { data: PaginatedResponse<Shift> } = await axios.get<PaginatedResponse<Shift>>(nextUrl);
    shifts.push(...response.data.data);
    nextUrl = response.data.links.next ? `${API_BASE_URL}${response.data.links.next}` : null;
  }

  return shifts;
}

async function getTopWorkplaces(): Promise<TopWorkplace[]> {
  const [workplaces, shifts] = await Promise.all([
    fetchAllWorkplaces(),
    fetchAllShifts()
  ]);
  
  // Count completed shifts per workplace (shifts with workerId and not cancelled)
  const shiftCounts = new Map<number, number>();
  
  shifts.forEach(shift => {
    if (shift.workerId !== null && shift.cancelledAt === null) {
      const currentCount = shiftCounts.get(shift.workplaceId) || 0;
      shiftCounts.set(shift.workplaceId, currentCount + 1);
    }
  });
  
  // Create workplace objects with shift counts
  const workplacesWithCounts = workplaces
    .filter(workplace => workplace.status === 0) // Only active workplaces
    .map(workplace => ({
      name: workplace.name,
      shifts: shiftCounts.get(workplace.id) || 0
    }))
    .sort((a, b) => b.shifts - a.shifts) // Sort by shift count descending
    .slice(0, 3); // Take top 3
  
  return workplacesWithCounts;
}

async function main() {
  try {
    const topWorkplaces = await getTopWorkplaces();
    console.log(JSON.stringify(topWorkplaces, null, 2));
  } catch (error) {
    console.error('Error fetching top workplaces:', error);
    process.exit(1);
  }
}

main();
