{"name": "red-planet-client", "version": "0.1.0", "type": "module", "description": "Client for Red Planet", "author": "Clipboard", "private": true, "license": "UNLICENSED", "scripts": {"start:dev": "vite", "build": "tsc && vite build", "lint": "eslint ./src --ext ts,tsx", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@tanstack/react-query": "^5.22.2", "axios": "^1.6.7", "date-fns": "^3.3.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "9.23.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "globals": "^15.9.0", "postcss": "^8.4.35", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}