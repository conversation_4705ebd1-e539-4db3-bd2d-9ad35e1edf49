{"name": "red-planet-server", "version": "0.1.0", "description": "Server for Red Planet", "author": "Clipboard", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"**/*.ts\" \"**/*.js\" \"**/*.yml\"  \"**/*.md\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:topWorkplaces": "npm run start:topWorkplaces:ts", "start:topWorkplaces:ts": "ts-node src/scripts/top-workplaces.ts", "start:topWorkplaces:js": "node src/scripts/top-workplaces.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ../test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@prisma/client": "6.5.0", "axios": "^1.6.7", "reflect-metadata": "^0.2.0", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "8.28.0", "@typescript-eslint/parser": "8.28.0", "eslint": "9.23.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-simple-import-sort": "12.1.1", "json5": "^2.2.3", "npm-check-updates": "^16.14.17", "prettier": "^3.0.0", "prisma": "6.5.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "typescript-eslint": "8.28.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}