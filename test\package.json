{"name": "red-planet-test", "scripts": {"start:topWorkplaces:combinations": "ts-node scripts/output-combinations.ts", "start:topWorkplaces:reference": "ts-node scripts/top-workplaces-reference.ts", "test:exercise": "jest --config ./jest-exercise.json", "test:exercise:quiet": "npm run --silent test:exercise -- --color=always 2>&1 | grep '✕\\|✓' --color=never", "reseed": "npx prisma migrate reset --force --skip-seed && npx prisma db seed -- --environment test"}, "prisma": {"seed": "ts-node ../server/prisma/seed.ts", "schema": "../server/prisma/schema.prisma"}, "devDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/testing": "10.0.0", "@types/jest": "29.5.14", "@types/supertest": "6.0.3", "jest": "^29.5.0", "jest-ctrf-json-reporter": "^0.0.9", "jest-junit": "^16.0.0", "supertest": "7.1.0", "ts-jest": "29.3.0", "ts-node": "^10.9.1"}}