const axios = require('axios');

async function testPagination() {
  try {
    console.log('Testing workplaces endpoint...');
    const response = await axios.get('http://localhost:3001/workplaces');
    console.log('First page response:');
    console.log('Data count:', response.data.data.length);
    console.log('Next link:', response.data.links.next);
    
    if (response.data.links.next) {
      console.log('\nTesting next page...');
      const nextResponse = await axios.get(response.data.links.next);
      console.log('Second page response:');
      console.log('Data count:', nextResponse.data.data.length);
      console.log('Next link:', nextResponse.data.links.next);
    }
  } catch (error) {
    console.error('Error:', error.message);
    if (error.config) {
      console.error('URL that failed:', error.config.url);
    }
  }
}

testPagination();
