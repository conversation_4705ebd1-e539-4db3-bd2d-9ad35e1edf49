name: Basic Functionality Check

on:
  pull_request:
    branches: ["**"]
  workflow_dispatch:

jobs:
  basic-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install server dependencies
        working-directory: ./server
        run: npm install

      - name: Start server
        working-directory: ./server
        run: npm run start &

      - name: Wait for server to start
        run: sleep 10

      - name: Install test dependencies
        working-directory: ./test
        run: npm install

      - name: Set up database and seed data
        working-directory: ./server
        run: npx prisma migrate dev --name init

      - name: Run basic functionality tests
        working-directory: ./test
        run: npx jest --config ./jest-basic.json
