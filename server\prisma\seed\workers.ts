import { Prisma } from "@prisma/client";

export const workers: Prisma.WorkerCreateInput[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    status: 0,
  },
  {
    name: "<PERSON><PERSON>",
    status: 2,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON><PERSON>",
    status: 1,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON><PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 2,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 1,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 1,
  },
  {
    name: "<PERSON><PERSON>",
    status: 2,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON><PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
  {
    name: "<PERSON>",
    status: 0,
  },
];
